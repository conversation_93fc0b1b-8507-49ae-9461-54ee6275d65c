<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="author" content="lean">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateC̵̈́͜͝ṛ̷̨̑a̴̰̋̃s̵̳̏h̶̡͂͝</title>
    <link rel="stylesheet" href="/toastify.min.css">
    <style>
        *,
        ::after,
        ::before {
            box-sizing: border-box;
        }

        body {
            background-color: #212121;
            color: #fff;
            font-family: monospace, serif;
            letter-spacing: 0.05em;
        }

        h1 {
            font-size: 23px;
        }

        .form {
            width: 300px;
            padding: 64px 15px 24px;
            margin: 0 auto;
        }

        .form .control {
            margin: 0 0 24px;
        }

        .form .control input {
            width: 100%;
            padding: 14px 16px;
            border: 0;
            background: transparent;
            color: #fff;
            font-family: monospace, serif;
            letter-spacing: 0.05em;
            font-size: 16px;
        }

        .form .control input:hover,
        .form .control input:focus {
            outline: none;
            border: 0;
        }

        .form .btn {
            width: 100%;
            display: block;
            padding: 14px 16px;
            background: transparent;
            outline: none;
            border: 0;
            color: #fff;
            letter-spacing: 0.1em;
            font-weight: bold;
            font-family: monospace;
            font-size: 16px;
        }

        .block-cube {
            position: relative;
        }

        .block-cube .bg-top {
            position: absolute;
            height: 10px;
            background: #020024;
            background: linear-gradient(90deg, #020024 0%, #340979 37%, #00d4ff 94%);
            bottom: 100%;
            left: 5px;
            right: -5px;
            transform: skew(-45deg, 0);
            margin: 0;
        }

        .block-cube .bg-top .bg-inner {
            bottom: 0;
        }

        .block-cube .bg {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: #020024;
            background: linear-gradient(90deg, #020024 0%, #340979 37%, #00d4ff 94%);
        }

        .block-cube .bg-right {
            position: absolute;
            background: #020024;
            background: #00d4ff;
            top: -5px;
            z-index: 0;
            bottom: 5px;
            width: 10px;
            left: 100%;
            transform: skew(0, -45deg);
        }

        .block-cube .bg-right .bg-inner {
            left: 0;
        }

        .block-cube .bg .bg-inner {
            transition: all 0.2s ease-in-out;
        }

        .block-cube .bg-inner {
            background: #212121;
            position: absolute;
            left: 2px;
            top: 2px;
            right: 2px;
            bottom: 2px;
        }

        .block-cube .text {
            position: relative;
            z-index: 2;
        }

        .block-cube.block-input input {
            position: relative;
            z-index: 2;
        }

        .block-cube.block-input input:focus~.bg-right .bg-inner,
        .block-cube.block-input input:focus~.bg-top .bg-inner,
        .block-cube.block-input input:focus~.bg-inner .bg-inner {
            top: 100%;
            background: rgba(255, 255, 255, 0.5);
        }

        .block-cube.block-input .bg-top,
        .block-cube.block-input .bg-right,
        .block-cube.block-input .bg {
            background: rgba(255, 255, 255, 0.5);
            transition: background 0.2s ease-in-out;
        }

        .block-cube.block-input .bg-right .bg-inner,
        .block-cube.block-input .bg-top .bg-inner {
            transition: all 0.2s ease-in-out;
        }

        .block-cube.block-input:focus .bg-top,
        .block-cube.block-input:focus .bg-right,
        .block-cube.block-input:focus .bg,
        .block-cube.block-input:hover .bg-top,
        .block-cube.block-input:hover .bg-right,
        .block-cube.block-input:hover .bg {
            background: rgba(255, 255, 255, 0.8);
        }

        .block-cube.block-cube-hover:focus .bg .bg-inner,
        .block-cube.block-cube-hover:hover .bg .bg-inner {
            top: 100%;
        }

        .credits {
            position: fixed;
            left: 0;
            bottom: 0;
            padding: 15px 15px;
            width: 100%;
            z-index: 111;
        }

        .credits a {
            opacity: 0.6;
            color: #fff;
            font-size: 11px;
            text-decoration: none;
        }

        .credits a:hover {
            opacity: 1;
        }

    </style>
</head>

<body>
    <form autocomplete="off" class="form">
        <div class="control">
            <h1>
                Sign In
            </h1>
        </div>
        <div class="control block-cube block-input">
            <input id="username" name="username" placeholder="Username" type="text">
            <div class="bg-top">
                <div class="bg-inner"></div>
            </div>
            <div class="bg-right">
                <div class="bg-inner"></div>
            </div>
            <div class="bg">
                <div class="bg-inner"></div>
            </div>
        </div>
        <div class="control block-cube block-input">
            <input id="password" name="password" placeholder="Password" type="password">
            <div class="bg-top">
                <div class="bg-inner"></div>
            </div>
            <div class="bg-right">
                <div class="bg-inner"></div>
            </div>
            <div class="bg">
                <div class="bg-inner"></div>
            </div>
        </div>
        <button id="loginButton" class="btn block-cube block-cube-hover" type="button">
            <div class="bg-top">
                <div class="bg-inner"></div>
            </div>
            <div class="bg-right">
                <div class="bg-inner"></div>
            </div>
            <div class="bg">
                <div class="bg-inner"></div>
            </div>
            <div class="text">
                Log In
            </div>
        </button>
    </form>
    <script src="/toastify.js"></script>
    <script>
        window.onload = () => {
            const userInput = document.getElementById("username");
            const passwordInput = document.getElementById("password");
            const loginButton = document.getElementById("loginButton");

            loginButton.addEventListener("click", async () => {
                const formData = new URLSearchParams();
                formData.append("username", userInput.value);
                formData.append("password", passwordInput.value);
                
                try {
                    const requestOptions = {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded"
                        },
                        body: formData,
                    };

                    const response = await fetch("/user", requestOptions);

                    if (!response.ok) {
                        showErrorToast("API error");
                    } else {
                        const data = await response.json();
                        showSuccessToast(data.msg);
                    }
                } catch (error) {
                    showErrorToast("Network error");
                }
            });

            function showErrorToast(message) {
                Toastify({
                    text: message,
                    style: {
                        background: "#e20404",
                    },
                }).showToast();
            }

            function showSuccessToast(message) {
                Toastify({
                    text: message,
                    style: {
                        background: "#04bce2",
                    },
                }).showToast();
            }
        };

    </script>
</body>

</html>